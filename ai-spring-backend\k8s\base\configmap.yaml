apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-spring-backend-config
  namespace: ai-spring-backend-dev
  labels:
    app: ai-spring-backend
    app.kubernetes.io/name: ai-spring-backend
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: ai-spring-backend
    environment: dev
    app.display-name: "ai-spring-backend"
  annotations:
    argocd.argoproj.io/sync-wave: "1"
data:
  # Application Configuration
  NODE_ENV: "dev"
  PORT: "8080"
  APP_NAME: "ai-spring-backend"
  APP_TYPE: "springboot-backend"

  # Database Configuration (if enabled)
  DB_HOST: "DB_HOST"
  DB_PORT: "DB_PORT"
  DB_NAME: "DB_NAME"
  DB_USER: "DB_USER"

  # SMTP Configuration
  SMTP_HOST: "smtp.gmail.com"
  SMTP_PORT: "587"
  SMTP_FROM: "<EMAIL>"

  # Dynamic Backend Configuration (for frontend applications)
  API_URL: "http://ai-spring-backend-service:8080"
  BACKEND_TYPE: "springboot-backend"
  BACKEND_PROJECT_ID: "ai-spring-backend"
  BACKEND_PORT: "8080"
  BACKEND_NAMESPACE: "ai-spring-backend-dev"

  # Frontend-specific environment variables
  REACT_APP_API_URL: "http://ai-spring-backend-service:8080"
  REACT_APP_BACKEND_TYPE: "springboot-backend"
  VUE_APP_API_URL: "http://ai-spring-backend-service:8080"
  VUE_APP_BACKEND_TYPE: "springboot-backend"
  ANGULAR_API_URL: "http://ai-spring-backend-service:8080"
  ANGULAR_BACKEND_TYPE: "springboot-backend"

  # Service Discovery Configuration
  SERVICE_NAME: "ai-spring-backend-service"
  SERVICE_NAMESPACE: "ai-spring-backend-dev"
  SERVICE_PORT: "8080"

  # Application-specific configurations will be added by overlays
